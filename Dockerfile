# 使用 Ubuntu 22.04 作为基础镜像
FROM ubuntu:22.04

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHON_VERSION=3.11

# 更新系统并安装 Python 和必要的依赖
RUN apt-get update && apt-get install -y \
    # Python 相关
    python3.11 \
    python3.11-dev \
    python3.11-distutils \
    python3-pip \
    # ChromeDriver 运行所需的系统库
    libglib2.0-0 \
    libnss3 \
    libgconf-2-4 \
    libxss1 \
    libappindicator3-1 \
    libindicator7 \
    gconf-service \
    libgtk-3-0 \
    libx11-6 \
    libx11-xcb1 \
    libxcb1 \
    libxcomposite1 \
    libxcursor1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxi6 \
    libxrandr2 \
    libxrender1 \
    libxtst6 \
    libasound2 \
    libpangocairo-1.0-0 \
    libatk1.0-0 \
    libcairo-gobject2 \
    libgdk-pixbuf2.0-0 \
    libdrm2 \
    libxkbcommon0 \
    libatspi2.0-0 \
    # 网络和工具
    curl \
    wget \
    unzip \
    ca-certificates \
    # 清理缓存
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 创建 python 和 pip 的软链接
RUN ln -s /usr/bin/python3.11 /usr/bin/python \
    && ln -s /usr/bin/pip3 /usr/bin/pip

# 升级 pip
RUN pip install --upgrade pip

# 复制requirements.txt并安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p /app/screenshots \
    && mkdir -p /app/temp_chrome_profile \
    && mkdir -p /app/driver

# 创建启动脚本
RUN echo '#!/bin/bash\n\
# 启动应用\n\
exec python main.py' > /app/start.sh \
    && chmod +x /app/start.sh

# 暴露端口
EXPOSE 8042

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8042/health || exit 1

# 启动应用
CMD ["/app/start.sh"]
