�f�5            �f�5            �f�5            tʁ0�           
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ְ�10rX          
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther (�ְ�10��b           
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ְ�10�v��           
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ְ�10�z��`           
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ְ�105	�          
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ְ�10?��6          37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ְ�10�ኧa          37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ְ�10Ƹ�W	          37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ְ�10e��� 
          37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ְ�10ʃ�71           	39_config
��؈��O�ԓ �ְ�1w��f~           	39_configf
��؈��O�ԓ �ְ�1
����Ą���ԓ �ְ�1
�����ٝ���ԓ �ְ�1
�����ؿ���ԓ �ְ�1Yj�� 
          	39_config�
��؈��O�ԓ �ְ�1
����Ą���ԓ �ְ�1
�����ٝ���ԓ �ְ�1
�����ؿ���ԓ �ְ�1
�ހ���`�ԓ �ְ�1
"�������d�ԓ �ְ�1(���ʖ������(x�           	39_config�
��؈��O�ԓ �ְ�1
����Ą���ԓ �ְ�1
�����ٝ���ԓ �ְ�1
�����ؿ���ԓ �ְ�1
�ހ���`�ԓ �ְ�1
"�������d�ԓ �ְ�1(���ʖ����
ۯ��Њ���ԓ �ְ�1�Vi;          	39_config�
��؈��O�ԓ �ְ�1
����Ą���ԓ �ְ�1
�����ٝ���ԓ �ְ�1
�����ؿ���ԓ �ְ�1
�ހ���`�ԓ �ְ�1
"�������d�ԓ �ְ�1(���ʖ����
ۯ��Њ���ԓ �ְ�1
��՛�����ԓ �ְ�1
���Åօ�C�ԓ �ְ�1
�����Ӆ���ԓ �ְ�1
��������_�ԓ �ְ�14HxӶ          	39_config�
��؈��O�ԓ �ְ�1
����Ą���ԓ �ְ�1
�����ٝ���ԓ �ְ�1
�����ؿ���ԓ �ְ�1
�ހ���`�ԓ �ְ�1
"�������d�ԓ �ְ�1(���ʖ����
ۯ��Њ���ԓ �ְ�1
��՛�����ԓ �ְ�1
���Åօ�C�ԓ �ְ�1
�����Ӆ���ԓ �ְ�1
��������_�ԓ �ְ�1
�����������I �ְ�1
�������I �ְ�1
ʒ���қ�C��I �ְ�1
���޾���,��I �ְ�1
���������I �ְ�1BM�jP          	39_config�
��؈��O�ԓ �ְ�1
����Ą���ԓ �ְ�1
�����ٝ���ԓ �ְ�1
�����ؿ���ԓ �ְ�1
�ހ���`�ԓ �ְ�1
"�������d�ԓ �ְ�1(���ʖ����
ۯ��Њ���ԓ �ְ�1
��՛�����ԓ �ְ�1
���Åօ�C�ԓ �ְ�1
�����Ӆ���ԓ �ְ�1
��������_�ԓ �ְ�1
�����������I �ְ�1
�������I �ְ�1
ʒ���қ�C��I �ְ�1
���޾���,��I �ְ�1
���������I �ְ�1
������t�ԓ �ְ�1
��������k�ԓ �ְ�1
գ��������ԓ �ְ�1
��ר�ٳ���ԓ �ְ�1
ෛ�������ԓ �ְ�1
������Ʉ��ԓ �ְ�1h��          	39_config�
��؈��O�ԓ �ְ�1
����Ą���ԓ �ְ�1
�����ٝ���ԓ �ְ�1
�����ؿ���ԓ �ְ�1
�ހ���`�ԓ �ְ�1
"�������d�ԓ �ְ�1(���ʖ����
ۯ��Њ���ԓ �ְ�1
��՛�����ԓ �ְ�1
���Åօ�C�ԓ �ְ�1
�����Ӆ���ԓ �ְ�1
��������_�ԓ �ְ�1
�����������I �ְ�1
�������I �ְ�1
ʒ���қ�C��I �ְ�1
���޾���,��I �ְ�1
���������I �ְ�1
������t�ԓ �ְ�1
��������k�ԓ �ְ�1
գ��������ԓ �ְ�1
��ר�ٳ���ԓ �ְ�1
ෛ�������ԓ �ְ�1
������Ʉ��ԓ �ְ�1
"��ї�Z�ԓ �ְ�1(Ȏ�������
#�򖐩�����ԓ �ְ�1(Ȏ�������
#�ɕԺ����ԓ �ְ�1(Ȏ�����������           20_1_1
1��'�            o��
       ,   4_IPH_BatterySaverMode
IPH_BatterySaverMode؞4_IPH_CompanionSidePanel
IPH_CompanionSidePanel؞$4_IPH_CompanionSidePanelRegionSearch(
"IPH_CompanionSidePanelRegionSearch؞4_IPH_DesktopCustomizeChrome 
IPH_DesktopCustomizeChrome؞4_IPH_DiscardRing
IPH_DiscardRing؞4_IPH_DownloadEsbPromo
IPH_DownloadEsbPromo؞/4_IPH_ExplicitBrowserSigninPreferenceRemembered3
-IPH_ExplicitBrowserSigninPreferenceRemembered؞4_IPH_HistorySearch
IPH_HistorySearch؞&4_IPH_FocusHelpBubbleScreenReaderPromo*
$IPH_FocusHelpBubbleScreenReaderPromo؞4_IPH_GMCCastStartStop
IPH_GMCCastStartStop؞4_IPH_GMCLocalMediaCasting
IPH_GMCLocalMediaCasting؞4_IPH_HighEfficiencyMode
IPH_HighEfficiencyMode؞4_IPH_LiveCaption
IPH_LiveCaption؞(4_IPH_PasswordsManagementBubbleAfterSave,
&IPH_PasswordsManagementBubbleAfterSave؞+4_IPH_PasswordsManagementBubbleDuringSignin/
)IPH_PasswordsManagementBubbleDuringSignin؞"4_IPH_PasswordsWebAppProfileSwitch&
 IPH_PasswordsWebAppProfileSwitch؞4_IPH_PasswordSharingFeature 
IPH_PasswordSharingFeature؞4_IPH_PdfSearchifyFeature
IPH_PdfSearchifyFeature؞*4_IPH_PerformanceInterventionDialogFeature.
(IPH_PerformanceInterventionDialogFeature؞-4_IPH_PriceInsightsPageActionIconLabelFeature1
+IPH_PriceInsightsPageActionIconLabelFeature؞&4_IPH_PriceTrackingEmailConsentFeature*
$IPH_PriceTrackingEmailConsentFeature؞-4_IPH_PriceTrackingPageActionIconLabelFeature1
+IPH_PriceTrackingPageActionIconLabelFeature؞4_IPH_ReadingModeSidePanel
IPH_ReadingModeSidePanel؞4_IPH_ShoppingCollectionFeature#
IPH_ShoppingCollectionFeature؞%4_IPH_SidePanelGenericPinnableFeature)
#IPH_SidePanelGenericPinnableFeature؞)4_IPH_SidePanelLensOverlayPinnableFeature-
'IPH_SidePanelLensOverlayPinnableFeature؞14_IPH_SidePanelLensOverlayPinnableFollowupFeature5
/IPH_SidePanelLensOverlayPinnableFollowupFeature؞4_IPH_SignoutWebIntercept
IPH_SignoutWebIntercept؞4_IPH_TabGroupsSaveV2Intro
IPH_TabGroupsSaveV2Intro؞4_IPH_TabGroupsSaveV2CloseGroup#
IPH_TabGroupsSaveV2CloseGroup؞4_IPH_ProfileSwitch
IPH_ProfileSwitch؞4_IPH_PriceTrackingInSidePanel"
IPH_PriceTrackingInSidePanel؞4_IPH_PwaQuietNotification
IPH_PwaQuietNotification؞4_IPH_AutofillAiOptIn
IPH_AutofillAiOptIn؞.4_IPH_AutofillExternalAccountProfileSuggestion2
,IPH_AutofillExternalAccountProfileSuggestion؞&4_IPH_AutofillVirtualCardCVCSuggestion*
$IPH_AutofillVirtualCardCVCSuggestion؞#4_IPH_AutofillVirtualCardSuggestion'
!IPH_AutofillVirtualCardSuggestion؞4_IPH_CookieControls
IPH_CookieControls؞$4_IPH_DesktopPWAsLinkCapturingLaunch(
"IPH_DesktopPWAsLinkCapturingLaunch؞,4_IPH_DesktopPWAsLinkCapturingLaunchAppInTab0
*IPH_DesktopPWAsLinkCapturingLaunchAppInTab؞!4_IPH_SupervisedUserProfileSignin%
IPH_SupervisedUserProfileSignin؞4_IPH_iOSPasswordPromoDesktop!
IPH_iOSPasswordPromoDesktop؞4_IPH_iOSAddressPromoDesktop 
IPH_iOSAddressPromoDesktop؞4_IPH_iOSPaymentPromoDesktop 
IPH_iOSPaymentPromoDesktop؞�]R��@          
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    �������$


   ?ShoppingUserOther  (�ְ�10/�I; A          #38_h       OG�A<T   �+y   �+y	
�%��