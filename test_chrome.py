#!/usr/bin/env python3
"""
测试Chrome在Docker容器中是否能正常工作
"""

import os
import platform
from selenium import webdriver
import undetected_chromedriver as uc

def test_chrome():
    """测试Chrome驱动"""
    try:
        print("开始测试Chrome驱动...")
        
        # 创建Chrome选项
        chrome_options = webdriver.ChromeOptions()
        
        # 根据操作系统设置Chrome二进制文件路径
        system = platform.system().lower()
        if system == "linux":
            # 在Docker容器中，使用系统安装的Chrome
            chrome_options.binary_location = "/usr/bin/google-chrome"
            print("使用Linux系统的Chrome")
        else:
            print("使用Windows系统的Chrome")
        
        # Docker容器中运行Chrome的必要参数
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--user-data-dir=/tmp/chrome-user-data')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        print("Chrome选项配置完成")
        
        # 创建驱动
        driver = uc.Chrome(options=chrome_options)
        print("Chrome驱动创建成功")
        
        # 测试访问网页
        driver.get("https://www.google.com")
        print(f"成功访问Google，页面标题: {driver.title}")
        
        # 关闭驱动
        driver.quit()
        print("测试完成，Chrome驱动工作正常")
        return True
        
    except Exception as e:
        print(f"Chrome驱动测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_chrome()
    exit(0 if success else 1)
